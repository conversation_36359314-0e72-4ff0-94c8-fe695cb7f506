package com.zsmall.activity.biz.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * 活动过期消息消费者
 * 监听活动过期消息，具体业务逻辑由用户自行实现
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ProductActivityExpireConsumer {

    private final ProductActiveService productActiveService;

    @PostConstruct
    public void init() {
        log.info("活动过期消息消费者初始化完成，监听队列: {}", RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
    }
    /**
     * 监听活动过期消息
     * 当消息TTL过期时，消息会被转发到这个队列
     *
     * @param message 消息对象
     * @param channel RabbitMQ通道
     * @param deliveryTag 消息投递标签
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE)
    public void handleActivityExpire(Message message,
                                   @Header(AmqpHeaders.CHANNEL) Channel channel,
                                   @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        String activeCode = null;
        boolean processSuccess = false;
        try {
            // 获取消息内容（活动编码）
            String messageBody = new String(message.getBody());
            log.info("收到活动过期消息: {}", messageBody);

            ActivityExpireMq mq = JSONUtil.toBean(messageBody, ActivityExpireMq.class);
            if (ObjectUtil.isNull(mq)){
              throw new RuntimeException("消息内容为空");
            }
            if (StrUtil.isEmpty(mq.getActiveCode())){
              throw new RuntimeException("活动编码为空");
            }
            if (ObjectUtil.isNull(mq.getType())){
                throw new RuntimeException("活动类型为空");
            }

            activeCode = mq.getActiveCode();
            log.info("开始处理活动过期: 活动编码={}, 活动类型={}", activeCode, mq.getType());

            if (mq.getType() == 1){
                productActiveService.handleSupplierActivityExpire(mq.getActiveCode());
            }else {
                productActiveService.handleDistributorActivityExpire(mq.getActiveCode());
            }

            processSuccess = true;
            log.info("活动过期处理完成: {}", activeCode);

        } catch (Exception e) {
            log.error("处理活动过期消息失败: activeCode={}", activeCode, e);
            // 处理失败时，可以选择是否重新入队
            // 这里选择确认消息，避免无限重试
        } finally {
            try {
                // 无论成功失败都确认消息，避免消息堆积
                channel.basicAck(deliveryTag, false);
                log.debug("消息确认完成: activeCode={}, success={}", activeCode, processSuccess);
            } catch (IOException e) {
                log.error("确认消息失败: activeCode={}", activeCode, e);
            }
        }
    }

}
