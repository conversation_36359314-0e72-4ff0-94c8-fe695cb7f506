package com.zsmall.activity.biz.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ插件检查工具
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RabbitMqPluginChecker {
    
    private final RabbitAdmin rabbitAdmin;
    
    /**
     * 检查延时消息插件是否可用
     */
    public boolean isDelayedMessagePluginAvailable() {
        try {
            // 尝试创建一个测试用的延时交换机
            Map<String, Object> arguments = new HashMap<>();
            arguments.put("x-delayed-type", "direct");
            
            org.springframework.amqp.core.CustomExchange testExchange = 
                new org.springframework.amqp.core.CustomExchange(
                    "test-delay-plugin-check", 
                    "x-delayed-message", 
                    false, // 不持久化
                    true,  // 自动删除
                    arguments
                );
            
            // 尝试声明交换机
            rabbitAdmin.declareExchange(testExchange);
            
            // 如果成功，删除测试交换机
            rabbitAdmin.deleteExchange("test-delay-plugin-check");
            
            log.info("RabbitMQ延时消息插件检查通过");
            return true;
            
        } catch (Exception e) {
            log.warn("RabbitMQ延时消息插件不可用: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取插件状态信息
     */
    public Map<String, Object> getPluginStatus() {
        Map<String, Object> status = new HashMap<>();
        
        boolean available = isDelayedMessagePluginAvailable();
        status.put("delayedMessagePlugin", available);
        status.put("recommendation", available ? 
            "建议使用延时插件方案" : 
            "插件不可用，建议使用TTL+死信队列方案");
            
        return status;
    }
}
