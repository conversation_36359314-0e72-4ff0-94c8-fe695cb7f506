package com.zsmall.activity.biz.controller;

import com.zsmall.activity.biz.util.RabbitMqPluginChecker;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * RabbitMQ状态检查控制器
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/rabbitmq")
@RequiredArgsConstructor
public class RabbitMqStatusController {
    
    private final RabbitMqPluginChecker pluginChecker;
    
    /**
     * 检查RabbitMQ插件状态
     */
    @GetMapping("/plugin-status")
    public Map<String, Object> checkPluginStatus() {
        return pluginChecker.getPluginStatus();
    }
}
