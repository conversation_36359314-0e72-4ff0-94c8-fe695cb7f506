package com.zsmall.activity.biz.config;

import com.hengjian.stream.mq.constant.RabbitMqConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ健康检查配置
 * 用于检查消费者启动状态和队列连接情况
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
public class RabbitMqHealthChecker {

    private final RabbitAdmin rabbitAdmin;
    private final RabbitListenerEndpointRegistry registry;

    @Bean
    public ApplicationRunner rabbitMqHealthCheck() {
        return args -> {
            log.info("=== RabbitMQ健康检查开始 ===");
            
            try {
                // 检查队列是否存在
                checkQueue(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE);
                checkQueue(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
                
                // 检查消费者容器状态
                checkListenerContainers();
                
                log.info("=== RabbitMQ健康检查完成 ===");
                
            } catch (Exception e) {
                log.error("RabbitMQ健康检查失败", e);
            }
        };
    }
    
    private void checkQueue(String queueName) {
        try {
            var queueInfo = rabbitAdmin.getQueueInfo(queueName);
            if (queueInfo != null) {
                log.info("队列 [{}] 状态正常，消息数量: {}, 消费者数量: {}", 
                    queueName, queueInfo.getMessageCount(), queueInfo.getConsumerCount());
            } else {
                log.warn("队列 [{}] 不存在或无法访问", queueName);
            }
        } catch (Exception e) {
            log.error("检查队列 [{}] 失败", queueName, e);
        }
    }
    
    private void checkListenerContainers() {
        log.info("检查监听器容器状态:");
        registry.getListenerContainerIds().forEach(id -> {
            var container = registry.getListenerContainer(id);
            if (container != null) {
                log.info("监听器容器 [{}] - 运行状态: {}, 活跃状态: {}", 
                    id, container.isRunning(), container.isActive());
            }
        });
        
        if (registry.getListenerContainerIds().isEmpty()) {
            log.warn("未发现任何监听器容器！这可能是问题所在。");
        }
    }
}
