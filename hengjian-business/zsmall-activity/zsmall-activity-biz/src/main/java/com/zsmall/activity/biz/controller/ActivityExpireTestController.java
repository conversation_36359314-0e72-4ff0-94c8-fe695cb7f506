package com.zsmall.activity.biz.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 活动过期测试控制器
 * 用于测试MQ消息发送和消费功能
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/test/activity-expire")
@Slf4j
@RequiredArgsConstructor
public class ActivityExpireTestController {

    private final RabbitTemplate rabbitTemplate;
    private final RabbitAdmin rabbitAdmin;
    private final RabbitListenerEndpointRegistry registry;

    /**
     * 发送测试消息（短TTL，用于快速测试）
     */
    @PostMapping("/send-test-message")
    public Map<String, Object> sendTestMessage(@RequestParam(defaultValue = "TEST_ACTIVITY_001") String activeCode,
                                               @RequestParam(defaultValue = "1") int type,
                                               @RequestParam(defaultValue = "10") long ttlSeconds) {
        Map<String, Object> result = new HashMap<>();
        try {
            ActivityExpireMq mq = new ActivityExpireMq();
            mq.setActiveCode(activeCode);
            mq.setType(type);
            String messageBody = JSONUtil.toJsonStr(mq);
            
            long ttlMs = ttlSeconds * 1000;
            
            // 发送消息到TTL队列
            rabbitTemplate.convertAndSend(
                RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE,
                RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY,
                messageBody,
                message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(ttlMs));
                    return message;
                }
            );
            
            result.put("success", true);
            result.put("message", "测试消息发送成功");
            result.put("activeCode", activeCode);
            result.put("type", type);
            result.put("ttlSeconds", ttlSeconds);
            result.put("expectedExpireTime", DateUtil.formatDateTime(DateUtil.offsetSecond(new Date(), (int) ttlSeconds)));
            
            log.info("发送测试消息成功: activeCode={}, type={}, ttl={}秒", activeCode, type, ttlSeconds);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "发送测试消息失败: " + e.getMessage());
            log.error("发送测试消息失败", e);
        }
        return result;
    }

    /**
     * 检查队列状态
     */
    @GetMapping("/queue-status")
    public Map<String, Object> checkQueueStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 检查TTL队列
            var ttlQueueInfo = rabbitAdmin.getQueueInfo(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE);
            result.put("ttlQueue", ttlQueueInfo != null ? 
                Map.of("name", RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE,
                       "messageCount", ttlQueueInfo.getMessageCount(),
                       "consumerCount", ttlQueueInfo.getConsumerCount()) : 
                "队列不存在");
            
            // 检查处理队列
            var processQueueInfo = rabbitAdmin.getQueueInfo(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE);
            result.put("processQueue", processQueueInfo != null ? 
                Map.of("name", RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE,
                       "messageCount", processQueueInfo.getMessageCount(),
                       "consumerCount", processQueueInfo.getConsumerCount()) : 
                "队列不存在");
            
            result.put("success", true);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查队列状态失败: " + e.getMessage());
            log.error("检查队列状态失败", e);
        }
        return result;
    }

    /**
     * 检查监听器状态
     */
    @GetMapping("/listener-status")
    public Map<String, Object> checkListenerStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> listeners = new HashMap<>();
            
            registry.getListenerContainerIds().forEach(id -> {
                var container = registry.getListenerContainer(id);
                if (container != null) {
                    listeners.put(id, Map.of(
                        "running", container.isRunning(),
                        "active", container.isActive(),
                        "autoStartup", container.isAutoStartup()
                    ));
                }
            });
            
            result.put("listeners", listeners);
            result.put("listenerCount", listeners.size());
            result.put("success", true);
            
            if (listeners.isEmpty()) {
                result.put("warning", "未发现任何监听器容器，这可能是问题所在！");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查监听器状态失败: " + e.getMessage());
            log.error("检查监听器状态失败", e);
        }
        return result;
    }

    /**
     * 重启监听器
     */
    @PostMapping("/restart-listeners")
    public Map<String, Object> restartListeners() {
        Map<String, Object> result = new HashMap<>();
        try {
            int restartCount = 0;
            for (String id : registry.getListenerContainerIds()) {
                var container = registry.getListenerContainer(id);
                if (container != null) {
                    container.stop();
                    Thread.sleep(1000); // 等待停止
                    container.start();
                    restartCount++;
                    log.info("重启监听器容器: {}", id);
                }
            }
            
            result.put("success", true);
            result.put("message", "监听器重启完成");
            result.put("restartCount", restartCount);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "重启监听器失败: " + e.getMessage());
            log.error("重启监听器失败", e);
        }
        return result;
    }
}
